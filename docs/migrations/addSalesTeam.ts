import { internalMutation } from "../../convex/_generated/server";
import { v } from "convex/values";

export const addSalesTeamMembers = internalMutation({
  args: {},
  handler: async (ctx) => {
    console.log("🚀 Starting sales team migration...");
    
    const salesTeamMembers = [
      {
        name: "<PERSON>",
        email: "<EMAIL>", // Using placeholder emails
        telegram: "@nedyalkov14",
        telegramUrl: "https://t.me/nedyalkov14"
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        telegram: "@web3_fren",
        telegramUrl: "https://t.me/web3_fren"
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        telegram: "@lyndonbrookes",
        telegramUrl: "https://t.me/lyndonbrookes"
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        telegram: "@espnyc1969",
        telegramUrl: "https://t.me/espnyc1969"
      },
      {
        name: "Ani",
        email: "<EMAIL>",
        telegram: "@ani_mild",
        telegramUrl: "https://t.me/ani_mild"
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        telegram: "@JoeBigelli",
        telegramUrl: "https://t.me/JoeBigelli"
      },
      {
        name: "Oliver",
        email: "<EMAIL>",
        telegram: "@OliTervit",
        telegramUrl: "https://t.me/OliTervit"
      },
      {
        name: "Janis",
        email: "<EMAIL>",
        telegram: "@JanisIBC",
        telegramUrl: "https://t.me/JanisIBC"
      },
      {
        name: "Talha",
        email: "<EMAIL>",
        telegram: "@qureshi_sama",
        telegramUrl: "https://t.me/qureshi_sama"
      },
      {
        name: "Stephen",
        email: "<EMAIL>",
        telegram: "@Stephen_Goodluck",
        telegramUrl: "https://t.me/Stephen_Goodluck"
      },
      {
        name: "Carlos",
        email: "<EMAIL>",
        telegram: "@naosouseupai",
        telegramUrl: "https://t.me/naosouseupai"
      },
      {
        name: "Gabriel",
        email: "<EMAIL>",
        telegram: "@gsari_ibc",
        telegramUrl: "https://t.me/sarigabr"
      }
    ];

    const createdSalesMembers = [];

    for (const member of salesTeamMembers) {
      try {
        console.log(`📝 Creating sales member: ${member.name} (${member.telegram})`);
        
        // Check if user already exists
        const existingUser = await ctx.db
          .query("users")
          .filter((q) => q.eq(q.field("email"), member.email))
          .first();

        if (existingUser) {
          console.log(`⚠️  User ${member.name} already exists, skipping...`);
          continue;
        }

        const userId = await ctx.db.insert("users", {
          name: member.name,
          fullName: member.name,
          email: member.email,
          telegram: member.telegram,
          role: "sales",
          roles: ["sales"],
          approved: true,
          profileCompleted: true,
          tier: "trusted", // Default tier for sales
        });

        createdSalesMembers.push({
          id: userId,
          name: member.name,
          telegram: member.telegram
        });

        // Log the creation
        await ctx.db.insert("auditLogs", {
          actorUserId: userId, // Self-created for migration
          action: "SALES_MEMBER_CREATED",
          entity: `users/${userId}`,
          meta: { 
            name: member.name,
            telegram: member.telegram,
            migration: true
          },
          at: Date.now(),
        });

        console.log(`✅ Successfully created sales member: ${member.name}`);
      } catch (error) {
        console.error(`❌ Failed to create sales member ${member.name}:`, error);
      }
    }

    console.log(`🎉 Sales team migration completed! Created ${createdSalesMembers.length} members.`);
    
    return {
      success: true,
      created: createdSalesMembers.length,
      members: createdSalesMembers
    };
  },
});
