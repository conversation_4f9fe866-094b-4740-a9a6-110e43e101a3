import { useState } from "react";
import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";
import { StatusBadge } from "./StatusBadge";
import { Button } from "../../ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../ui/select";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "../../ui/dialog";
import { Label } from "../../ui/label";
import { Textarea } from "../../ui/textarea";
import { toast } from "sonner";

interface PartnerApplication {
  _id: Id<"users">;
  name?: string;
  fullName?: string;
  email?: string;
  companyName?: string;
  companyType?: string;
  roleTitle?: string;
  telegram?: string;
  whatsapp?: string;
  xProfile?: string;
  preferredCommunication?: string[];
  profileCompleted?: boolean;
  _creationTime: number;
}

interface SalesMember {
  _id: Id<"users">;
  name?: string;
  fullName?: string;
}

interface PartnerApplicationsTableProps {
  applications: PartnerApplication[];
  salesMembers: SalesMember[];
  loading?: boolean;
  onReviewApplication?: (
    partnerId: Id<"users">, 
    approved: boolean, 
    tier?: "trusted" | "elite" | "diamond",
    salesUserId?: Id<"users">,
    notes?: string
  ) => void;
}

export function PartnerApplicationsTable({ 
  applications, 
  salesMembers,
  loading = false,
  onReviewApplication
}: PartnerApplicationsTableProps) {
  const [reviewingApplication, setReviewingApplication] = useState<PartnerApplication | null>(null);
  const [reviewData, setReviewData] = useState({
    approved: true,
    tier: "trusted" as "trusted" | "elite" | "diamond",
    salesUserId: "",
    notes: ""
  });

  const handleReviewSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!onReviewApplication || !reviewingApplication) return;

    try {
      await onReviewApplication(
        reviewingApplication._id,
        reviewData.approved,
        reviewData.approved ? reviewData.tier : undefined,
        reviewData.approved && reviewData.salesUserId ? reviewData.salesUserId as Id<"users"> : undefined,
        reviewData.notes || undefined
      );
      setReviewingApplication(null);
      setReviewData({ approved: true, tier: "trusted", salesUserId: "", notes: "" });
      toast.success(`Application ${reviewData.approved ? "approved" : "rejected"} successfully`);
    } catch (error) {
      toast.error("Failed to review application");
    }
  };

  const handleReview = (application: PartnerApplication) => {
    setReviewingApplication(application);
    setReviewData({ approved: true, tier: "trusted", salesUserId: "", notes: "" });
  };

  const handleQuickApprove = async (partnerId: Id<"users">) => {
    if (!onReviewApplication) return;
    
    try {
      await onReviewApplication(partnerId, true, "trusted");
      toast.success("Application approved successfully");
    } catch (error) {
      toast.error("Failed to approve application");
    }
  };

  const handleQuickReject = async (partnerId: Id<"users">) => {
    if (!onReviewApplication) return;
    
    if (confirm("Are you sure you want to reject this application?")) {
      try {
        await onReviewApplication(partnerId, false);
        toast.success("Application rejected successfully");
      } catch (error) {
        toast.error("Failed to reject application");
      }
    }
  };

  const columns: Column<PartnerApplication>[] = [
    {
      key: "name",
      label: "Name",
      sortable: true,
      render: (app) => app.name || app.fullName || "-"
    },
    {
      key: "email",
      label: "Email",
      sortable: true,
      render: (app) => app.email || "-"
    },
    {
      key: "companyName",
      label: "Company",
      sortable: true,
      render: (app) => app.companyName || "-"
    },
    {
      key: "companyType",
      label: "Company Type",
      render: (app) => app.companyType || "-"
    },
    {
      key: "roleTitle",
      label: "Role",
      render: (app) => app.roleTitle || "-"
    },
    {
      key: "profileCompleted",
      label: "Profile Status",
      render: (app) => (
        <StatusBadge 
          status={app.profileCompleted ? "complete" : "incomplete"}
          variant={app.profileCompleted ? "success" : "warning"}
        />
      )
    },
    {
      key: "_creationTime",
      label: "Applied",
      sortable: true,
      render: (app) => new Date(app._creationTime).toLocaleDateString()
    },
    {
      key: "actions",
      label: "Actions",
      render: (app) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleReview(app)}
          >
            Review
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickApprove(app._id)}
            className="text-green-600 hover:text-green-800"
          >
            Quick Approve
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickReject(app._id)}
            className="text-red-600 hover:text-red-800"
          >
            Reject
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Partner Applications</h3>
        <div className="text-sm text-gray-600">
          {applications.length} pending applications
        </div>
      </div>

      {/* Review Dialog */}
      <Dialog open={!!reviewingApplication} onOpenChange={() => setReviewingApplication(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Review Partner Application</DialogTitle>
          </DialogHeader>
          
          {reviewingApplication && (
            <div className="space-y-6">
              {/* Application Details */}
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <h4 className="font-semibold">Application Details</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div><strong>Name:</strong> {reviewingApplication.name || reviewingApplication.fullName || "-"}</div>
                  <div><strong>Email:</strong> {reviewingApplication.email || "-"}</div>
                  <div><strong>Company:</strong> {reviewingApplication.companyName || "-"}</div>
                  <div><strong>Company Type:</strong> {reviewingApplication.companyType || "-"}</div>
                  <div><strong>Role:</strong> {reviewingApplication.roleTitle || "-"}</div>
                  <div><strong>Telegram:</strong> {reviewingApplication.telegram || "-"}</div>
                  <div><strong>WhatsApp:</strong> {reviewingApplication.whatsapp || "-"}</div>
                  <div><strong>X Profile:</strong> {reviewingApplication.xProfile || "-"}</div>
                </div>
              </div>

              {/* Review Form */}
              <form onSubmit={handleReviewSubmit} className="space-y-4">
                <div>
                  <Label>Decision</Label>
                  <Select 
                    value={reviewData.approved ? "approve" : "reject"} 
                    onValueChange={(value) => setReviewData({ ...reviewData, approved: value === "approve" })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="approve">Approve</SelectItem>
                      <SelectItem value="reject">Reject</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {reviewData.approved && (
                  <>
                    <div>
                      <Label>Assign Tier</Label>
                      <Select 
                        value={reviewData.tier} 
                        onValueChange={(value) => setReviewData({ ...reviewData, tier: value as "trusted" | "elite" | "diamond" })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="trusted">Trusted (5% commission)</SelectItem>
                          <SelectItem value="elite">Elite (7.5% commission)</SelectItem>
                          <SelectItem value="diamond">Diamond (10% commission)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Assign Salesperson (optional)</Label>
                      <Select 
                        value={reviewData.salesUserId} 
                        onValueChange={(value) => setReviewData({ ...reviewData, salesUserId: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a salesperson" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">No assignment</SelectItem>
                          {salesMembers.map((member) => (
                            <SelectItem key={member._id} value={member._id}>
                              {member.name || member.fullName || "Unknown"}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                <div>
                  <Label>Notes (optional)</Label>
                  <Textarea
                    value={reviewData.notes}
                    onChange={(e) => setReviewData({ ...reviewData, notes: e.target.value })}
                    placeholder="Add any notes about this decision..."
                    rows={3}
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setReviewingApplication(null)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    {reviewData.approved ? "Approve" : "Reject"} Application
                  </Button>
                </div>
              </form>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <DataTable
        data={applications}
        columns={columns}
        loading={loading}
        emptyMessage="No pending applications found"
      />
    </div>
  );
}
