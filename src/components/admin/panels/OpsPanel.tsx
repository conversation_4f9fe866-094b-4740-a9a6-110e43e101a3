import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../../../convex/_generated/dataModel";

import { AdminLayout } from "../shared/AdminLayout";
import { NavigationTabs } from "../shared/NavigationTabs";
import { StatsCard } from "../shared/StatsCard";
import { LeadTable } from "../shared/LeadTable";
import { DealTable } from "../shared/DealTable";
import { PartnerApplicationsTable } from "../shared/PartnerApplicationsTable";
import { SalesTeamTable } from "../shared/SalesTeamTable";

export function OpsPanel() {
  const [activeSection, setActiveSection] = useState("leads");

  // Data queries
  const allLeads = useQuery(api.leads.listMine, {});
  const allDeals = useQuery(api.deals.listForPartner, {});
  const leadStats = useQuery(api.admin.getLeadStatistics);
  const pendingApplications = useQuery(api.partnerApplications.listPendingApplications);
  const approvedPartners = useQuery(api.partnerApplications.listApprovedPartners);
  const applicationStats = useQuery(api.partnerApplications.getApplicationStats);
  const salesMembers = useQuery(api.salesTeam.listSalesMembers);
  const salesStats = useQuery(api.salesTeam.getSalesStats);

  // Mutations
  const approveLead = useMutation(api.leads.approve);
  const reviewApplication = useMutation(api.partnerApplications.reviewPartnerApplication);
  const updatePartnerTier = useMutation(api.partnerApplications.updatePartnerTier);
  const assignPartnerToSales = useMutation(api.partnerApplications.assignPartnerToSales);
  const createSalesMember = useMutation(api.salesTeam.createSalesMember);
  const updateSalesMember = useMutation(api.salesTeam.updateSalesMember);
  const removeSalesMember = useMutation(api.salesTeam.removeSalesMember);
  
  // Handlers
  const handleApproveLead = async (leadId: Id<"leads">, approved: boolean, details?: any) => {
    try {
      console.log(`🔍 Ops reviewing lead: ${leadId} - ${approved ? 'APPROVE' : 'REJECT'}`);
      await approveLead({
        leadId,
        approved,
        telegramGroupUrl: details?.telegramGroupUrl,
        dealType: details?.dealType,
        commissionPct: details?.commissionPct,
      });
      console.log(`✅ Lead ${approved ? 'approved' : 'rejected'} successfully`);
      toast.success(`Lead ${approved ? "approved and deal created" : "rejected"}`);
    } catch (error) {
      console.error(`❌ Failed to review lead ${leadId}:`, error);
      toast.error(error instanceof Error ? error.message : "Failed to update lead");
    }
  };

  const handleReviewApplication = async (
    partnerId: Id<"users">,
    approved: boolean,
    tier?: "trusted" | "elite" | "diamond",
    salesUserId?: Id<"users">,
    notes?: string
  ) => {
    try {
      console.log(`🔍 Ops reviewing partner application: ${partnerId} - ${approved ? 'APPROVE' : 'REJECT'}`);
      await reviewApplication({
        partnerId,
        approved,
        tier,
        salesUserId,
        notes
      });
      console.log(`✅ Partner application ${approved ? 'approved' : 'rejected'} successfully`);
      toast.success(`Application ${approved ? "approved" : "rejected"} successfully`);
    } catch (error) {
      console.error(`❌ Failed to review application ${partnerId}:`, error);
      toast.error(error instanceof Error ? error.message : "Failed to review application");
    }
  };

  const handleCreateSalesMember = async (data: { name: string; email: string; telegram?: string }) => {
    try {
      console.log(`🆕 Ops creating sales member: ${data.name} (${data.email})`);
      await createSalesMember(data);
      console.log(`✅ Sales member created successfully: ${data.name}`);
      toast.success("Sales member created successfully");
    } catch (error) {
      console.error(`❌ Failed to create sales member:`, error);
      toast.error(error instanceof Error ? error.message : "Failed to create sales member");
    }
  };

  const handleUpdateSalesMember = async (userId: Id<"users">, data: { name?: string; email?: string; telegram?: string }) => {
    try {
      console.log(`📝 Ops updating sales member: ${userId}`);
      await updateSalesMember({ userId, ...data });
      console.log(`✅ Sales member updated successfully: ${userId}`);
      toast.success("Sales member updated successfully");
    } catch (error) {
      console.error(`❌ Failed to update sales member ${userId}:`, error);
      toast.error(error instanceof Error ? error.message : "Failed to update sales member");
    }
  };

  const handleRemoveSalesMember = async (userId: Id<"users">) => {
    try {
      console.log(`🗑️ Ops removing sales member: ${userId}`);
      await removeSalesMember({ userId });
      console.log(`✅ Sales member removed successfully: ${userId}`);
      toast.success("Sales member removed successfully");
    } catch (error) {
      console.error(`❌ Failed to remove sales member ${userId}:`, error);
      toast.error(error instanceof Error ? error.message : "Failed to remove sales member");
    }
  };

  // Calculate stats
  const pendingLeads = allLeads?.filter(l => l.approved === undefined).length || 0;
  const approvedLeads = allLeads?.filter(l => l.approved === true).length || 0;
  const activeDeals = allDeals?.filter(d => d.status === "in_progress").length || 0;
  const closedDeals = allDeals?.filter(d => d.status === "closed").length || 0;
  const pendingApplicationsCount = pendingApplications?.length || 0;

  const tabs = [
    { id: "applications", label: "Partner Applications", count: pendingApplicationsCount },
    { id: "leads", label: "Lead Management", count: pendingLeads },
    { id: "deals", label: "Deal Overview" },
    { id: "sales", label: "Sales Team" },
    { id: "analytics", label: "Operations Analytics" },
  ];

  return (
    <AdminLayout 
      title="Operations Panel" 
      description="Lead management and deal operations"
    >
      <NavigationTabs 
        tabs={tabs}
        activeTab={activeSection}
        onTabChange={setActiveSection}
      />

      {activeSection === "applications" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Pending Applications"
              value={pendingApplicationsCount}
              description="Awaiting review"
              color="yellow"
            />
            <StatsCard
              title="Approved Partners"
              value={applicationStats?.approved || 0}
              description="Active partners"
              color="green"
            />
            <StatsCard
              title="Rejected Applications"
              value={applicationStats?.rejected || 0}
              description="Declined applications"
              color="red"
            />
            <StatsCard
              title="Approval Rate"
              value={applicationStats?.total ?
                `${Math.round((applicationStats.approved / applicationStats.total) * 100)}%` :
                "0%"
              }
              description="Historical average"
              color="purple"
            />
          </div>

          <PartnerApplicationsTable
            applications={pendingApplications || []}
            salesMembers={salesMembers || []}
            loading={!pendingApplications}
            onReviewApplication={handleReviewApplication}
          />
        </div>
      )}

      {activeSection === "leads" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Total Leads"
              value={allLeads?.length || 0}
              description="All submitted leads"
              color="blue"
            />
            <StatsCard
              title="Pending Review"
              value={pendingLeads}
              description="Awaiting approval"
              color="yellow"
            />
            <StatsCard
              title="Approved Leads"
              value={approvedLeads}
              description="Ready for engagement"
              color="green"
            />
            <StatsCard
              title="Conversion Rate"
              value={allLeads?.length ? `${Math.round((approvedLeads / allLeads.length) * 100)}%` : "0%"}
              description="Lead to approval"
              color="purple"
            />
          </div>

          <LeadTable
            leads={allLeads || []}
            loading={!allLeads}
            onApproveLead={handleApproveLead}
          />
        </div>
      )}

      {activeSection === "deals" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Active Deals"
              value={activeDeals}
              description="In progress"
              color="blue"
            />
            <StatsCard
              title="Closed Deals"
              value={closedDeals}
              description="Successfully completed"
              color="green"
            />
            <StatsCard
              title="Total Deal Value"
              value={`$${allDeals?.reduce((sum, d) => sum + (d.dealValueUsd || 0), 0).toLocaleString() || 0}`}
              description="Cumulative value"
              color="purple"
            />
            <StatsCard
              title="Average Deal Size"
              value={allDeals?.length ? 
                `$${Math.round((allDeals.reduce((sum, d) => sum + (d.dealValueUsd || 0), 0) / allDeals.length)).toLocaleString()}` : 
                "$0"
              }
              description="Per deal average"
              color="yellow"
            />
          </div>

          <DealTable
            deals={allDeals || []}
            loading={!allDeals}
            showActions={false}
            showFinancials={false}
          />
        </div>
      )}

      {activeSection === "sales" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Total Sales Members"
              value={salesStats?.totalMembers || 0}
              description="All sales team"
              color="blue"
            />
            <StatsCard
              title="Active Members"
              value={salesStats?.activeMembers || 0}
              description="Currently active"
              color="green"
            />
            <StatsCard
              title="Total Assignments"
              value={salesStats?.totalAssignments || 0}
              description="Partner assignments"
              color="purple"
            />
            <StatsCard
              title="Avg Assignments"
              value={salesStats?.activeMembers ?
                Math.round((salesStats.totalAssignments / salesStats.activeMembers) * 10) / 10 :
                0
              }
              description="Per sales member"
              color="yellow"
            />
          </div>

          <SalesTeamTable
            salesMembers={salesMembers || []}
            loading={!salesMembers}
            onCreateMember={handleCreateSalesMember}
            onUpdateMember={handleUpdateSalesMember}
            onRemoveMember={handleRemoveSalesMember}
          />
        </div>
      )}

      {activeSection === "analytics" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="Lead Velocity"
              value={allLeads?.filter(l => 
                new Date(l._creationTime) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
              ).length || 0}
              description="Last 7 days"
              color="blue"
            />
            <StatsCard
              title="Approval Rate"
              value={leadStats ? `${Math.round((leadStats.approved / leadStats.total) * 100)}%` : "0%"}
              description="Historical average"
              color="green"
            />
            <StatsCard
              title="Deal Close Rate"
              value={allDeals?.length ? 
                `${Math.round((closedDeals / allDeals.length) * 100)}%` : 
                "0%"
              }
              description="Success percentage"
              color="purple"
            />
            <StatsCard
              title="Pipeline Health"
              value={activeDeals + pendingLeads}
              description="Active pipeline"
              color="yellow"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div 
              className="p-6 rounded-lg"
              style={{
                backgroundColor: 'var(--surface-1)',
                border: '1px solid var(--border-default)'
              }}
            >
              <h3 className="text-size-2 mb-4" style={{ color: 'var(--foreground)' }}>Lead Status Breakdown</h3>
              {leadStats ? (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span style={{ color: 'var(--foreground)' }}>Warm Leads:</span>
                    <span 
                      className="px-2 py-1 rounded text-size-4"
                      style={{
                        backgroundColor: 'var(--status-warning-bg)',
                        color: 'var(--status-warning)'
                      }}
                    >
                      {leadStats.byStatus.warm}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span style={{ color: 'var(--foreground)' }}>Cold Leads:</span>
                    <span 
                      className="px-2 py-1 rounded text-size-4"
                      style={{
                        backgroundColor: 'var(--status-info-bg)',
                        color: 'var(--status-info)'
                      }}
                    >
                      {leadStats.byStatus.cold}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span style={{ color: 'var(--foreground)' }}>Won Leads:</span>
                    <span 
                      className="px-2 py-1 rounded text-size-4"
                      style={{
                        backgroundColor: 'var(--status-success-bg)',
                        color: 'var(--status-success)'
                      }}
                    >
                      {leadStats.byStatus.won}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span style={{ color: 'var(--foreground)' }}>Lost Leads:</span>
                    <span 
                      className="px-2 py-1 rounded text-size-4"
                      style={{
                        backgroundColor: 'var(--status-error-bg)',
                        color: 'var(--status-error)'
                      }}
                    >
                      {leadStats.byStatus.lost}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--accent)' }}></div>
                </div>
              )}
            </div>

            <div 
              className="p-6 rounded-lg"
              style={{
                backgroundColor: 'var(--surface-1)',
                border: '1px solid var(--border-default)'
              }}
            >
              <h3 className="text-size-2 mb-4" style={{ color: 'var(--foreground)' }}>Deal Performance</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span style={{ color: 'var(--foreground)' }}>In Progress:</span>
                  <span className="font-semibold" style={{ color: 'var(--status-info)' }}>{activeDeals}</span>
                </div>
                <div className="flex justify-between">
                  <span style={{ color: 'var(--foreground)' }}>Closed:</span>
                  <span className="font-semibold" style={{ color: 'var(--status-success)' }}>{closedDeals}</span>
                </div>
                <div className="flex justify-between">
                  <span style={{ color: 'var(--foreground)' }}>Lost:</span>
                  <span className="font-semibold" style={{ color: 'var(--status-error)' }}>
                    {allDeals?.filter(d => d.status === "lost").length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span style={{ color: 'var(--foreground)' }}>Success Rate:</span>
                  <span className="font-semibold" style={{ color: 'var(--accent)' }}>
                    {allDeals?.length ? 
                      `${Math.round((closedDeals / allDeals.length) * 100)}%` : 
                      "0%"
                    }
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}