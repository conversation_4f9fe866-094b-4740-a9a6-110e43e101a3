import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

export const create = mutation({
  args: {
    partnerId: v.id("users"),
    leadId: v.optional(v.id("leads")),
    projectName: v.string(),
    dealType: v.string(),
    status: v.union(v.literal("in_progress"), v.literal("closed"), v.literal("lost"), v.literal("paid")),
    totalTokens: v.optional(v.number()),
    receivedTokens: v.optional(v.number()),
    liquidatedTokens: v.optional(v.number()),
    liquidationUsd: v.optional(v.number()),
    dealValueUsd: v.optional(v.number()),
    commissionPct: v.number(),
    commissionDueTokenUsd: v.optional(v.number()),
    commissionDueFiatUsd: v.optional(v.number()),
    commissionPendingUsd: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const internal = await requireUser(ctx, ["sales", "ops", "accounting", "admin", "superadmin"]);
    
    // Auto-calculate commission if deal is created as closed/paid and has value
    let finalArgs = { ...args };
    
    const isClosedDeal = args.status === "closed" || args.status === "paid";
    
    if (isClosedDeal && args.dealValueUsd && args.dealValueUsd > 0) {
      // Only auto-calculate if commission isn't manually set
      if (!args.commissionDueFiatUsd) {
        // Get partner information for commission calculation
        const partner = await ctx.db.get(args.partnerId);
        if (!partner) {
          throw new Error("Partner not found");
        }
        
        // Commission rates based on partner tier
        const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
          switch (tier) {
            case "diamond": return 15; // 15%
            case "elite": return 12;   // 12%
            case "trusted": return 10; // 10%
            default: return 8;         // 8% for no tier
          }
        };
        
        const rate = getCommissionRate(partner.tier);
        const calculatedCommission = args.dealValueUsd * (rate / 100);
        
        finalArgs.commissionDueFiatUsd = calculatedCommission;
        finalArgs.commissionPct = rate;
        
        console.log(`Auto-calculated commission on create for ${partner.name || partner.email}: $${calculatedCommission} at ${rate}% (${partner.tier || 'no tier'})`);
      }
    }
    
    const dealId = await ctx.db.insert("deals", {
      ...finalArgs,
      salesUserId: internal._id,
      lastUpdatedAt: Date.now(),
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: internal._id,
      action: "DEAL_CREATED",
      entity: `deals/${dealId}`,
      meta: { projectName: finalArgs.projectName, dealValueUsd: finalArgs.dealValueUsd, commissionCalculated: isClosedDeal },
      at: Date.now(),
    });
    
    return dealId;
  },
});

export const update = mutation({
  args: {
    dealId: v.id("deals"),
    status: v.optional(v.union(v.literal("in_progress"), v.literal("closed"), v.literal("lost"), v.literal("paid"))),
    totalTokens: v.optional(v.number()),
    receivedTokens: v.optional(v.number()),
    liquidatedTokens: v.optional(v.number()),
    liquidationUsd: v.optional(v.number()),
    dealValueUsd: v.optional(v.number()),
    commissionPct: v.optional(v.number()),
    commissionDueTokenUsd: v.optional(v.number()),
    commissionDueFiatUsd: v.optional(v.number()),
    commissionPendingUsd: v.optional(v.number()),
  },
  handler: async (ctx, { dealId, ...updates }) => {
    const internal = await requireUser(ctx, ["sales", "ops", "accounting", "admin", "superadmin"]);
    
    // Get current deal data
    const currentDeal = await ctx.db.get(dealId);
    if (!currentDeal) {
      throw new Error("Deal not found");
    }
    
    // Get partner information for commission calculation
    const partner = await ctx.db.get(currentDeal.partnerId);
    if (!partner) {
      throw new Error("Partner not found");
    }
    
    // Commission rates based on partner tier
    const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
      switch (tier) {
        case "diamond": return 15; // 15%
        case "elite": return 12;   // 12%
        case "trusted": return 10; // 10%
        default: return 8;         // 8% for no tier
      }
    };
    
    // Auto-calculate commission if deal is being closed and has value
    let finalUpdates = { ...updates };
    
    const isClosingDeal = updates.status === "closed" || updates.status === "paid";
    const dealValue = updates.dealValueUsd || currentDeal.dealValueUsd;
    
    if (isClosingDeal && dealValue && dealValue > 0) {
      // Only auto-calculate if commission isn't manually set
      if (!updates.commissionDueFiatUsd && !currentDeal.commissionDueFiatUsd) {
        const rate = getCommissionRate(partner.tier);
        const calculatedCommission = dealValue * (rate / 100);
        
        finalUpdates.commissionDueFiatUsd = calculatedCommission;
        finalUpdates.commissionPct = rate;
        
        console.log(`Auto-calculated commission for ${partner.name || partner.email}: $${calculatedCommission} at ${rate}% (${partner.tier || 'no tier'})`);
      }
    }
    
    await ctx.db.patch(dealId, {
      ...finalUpdates,
      lastUpdatedAt: Date.now(),
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: internal._id,
      action: "DEAL_UPDATED",
      entity: `deals/${dealId}`,
      meta: finalUpdates,
      at: Date.now(),
    });
    
    return { ok: true };
  },
});

export const listForPartner = query({
  args: { 
    partnerId: v.optional(v.id("users")), 
    status: v.optional(v.string()) 
  },
  handler: async (ctx, { partnerId, status }) => {
    const viewer = await requireUser(ctx, "any");
    
    // Determine which partner's deals to show
    let targetPartnerId = partnerId;
    if (viewer.role === "partner") {
      targetPartnerId = viewer._id; // Partners can only see their own
    }
    
    if (!targetPartnerId) {
      // Internal users without specifying partnerId see all
      const deals = await ctx.db.query("deals").collect();
      return status ? deals.filter(d => d.status === status) : deals;
    }
    
    const deals = await ctx.db
      .query("deals")
      .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
      .collect();
      
    return status ? deals.filter(d => d.status === status) : deals;
  }
});

export const getDealSummary = query({
  args: { partnerId: v.optional(v.id("users")) },
  handler: async (ctx, { partnerId }) => {
    const viewer = await requireUser(ctx, "any");
    
    let targetPartnerId = partnerId;
    if (viewer.role === "partner") {
      targetPartnerId = viewer._id;
    }
    
    if (!targetPartnerId) {
      throw new Error("Partner ID required");
    }
    
    const deals = await ctx.db
      .query("deals")
      .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
      .collect();
    
    const summary = {
      totalDeals: deals.length,
      inProgress: deals.filter(d => d.status === "in_progress").length,
      closed: deals.filter(d => d.status === "closed").length,
      lost: deals.filter(d => d.status === "lost").length,
      paid: deals.filter(d => d.status === "paid").length,
      totalValue: deals.reduce((sum, d) => sum + (d.dealValueUsd || 0), 0),
      totalCommissionDue: deals.reduce((sum, d) => 
        sum + (d.commissionDueTokenUsd || 0) + (d.commissionDueFiatUsd || 0), 0),
      totalCommissionPending: deals.reduce((sum, d) => sum + (d.commissionPendingUsd || 0), 0),
    };
    
    return summary;
  }
});

// One-time fix for existing closed deals without commission values
export const fixExistingClosedDeals = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await requireUser(ctx, ["admin", "superadmin", "accounting"]);
    
    // Find all closed/paid deals without commission values
    const deals = await ctx.db.query("deals").collect();
    const dealsToFix = deals.filter(d => 
      (d.status === "closed" || d.status === "paid") && 
      d.dealValueUsd && 
      d.dealValueUsd > 0 && 
      (!d.commissionDueFiatUsd || d.commissionDueFiatUsd === 0)
    );
    
    console.log(`Found ${dealsToFix.length} deals to fix`);
    
    for (const deal of dealsToFix) {
      const partner = await ctx.db.get(deal.partnerId);
      if (!partner) continue;
      
      // Commission rates based on partner tier
      const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
        switch (tier) {
          case "diamond": return 15;
          case "elite": return 12;
          case "trusted": return 10;
          default: return 8;
        }
      };
      
      const rate = getCommissionRate(partner.tier);
      const commission = deal.dealValueUsd! * (rate / 100);
      
      await ctx.db.patch(deal._id, {
        commissionDueFiatUsd: commission,
        commissionPct: rate,
        lastUpdatedAt: Date.now(),
      });
      
      console.log(`Fixed deal ${deal.projectName}: $${commission} commission at ${rate}% for ${partner.name || partner.email}`);
    }
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: "BULK_FIX_COMMISSIONS",
      entity: "deals",
      meta: { dealsFixed: dealsToFix.length },
      at: Date.now(),
    });
    
    return { dealsFixed: dealsToFix.length };
  },
});
