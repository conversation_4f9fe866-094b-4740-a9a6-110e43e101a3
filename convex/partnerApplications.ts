import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

// List all pending partner applications
export const listPendingApplications = query({
  args: {},
  handler: async (ctx) => {
    console.log("📋 Fetching pending partner applications...");
    
    const pendingPartners = await ctx.db
      .query("users")
      .filter((q) => 
        q.and(
          q.eq(q.field("role"), "partner"),
          q.eq(q.field("approved"), undefined)
        )
      )
      .collect();

    console.log(`📊 Found ${pendingPartners.length} pending partner applications`);
    
    return pendingPartners.map(partner => ({
      _id: partner._id,
      name: partner.name,
      fullName: partner.fullName,
      email: partner.email,
      companyName: partner.companyName,
      companyType: partner.companyType,
      roleTitle: partner.roleTitle,
      telegram: partner.telegram,
      whatsapp: partner.whatsapp,
      xProfile: partner.xProfile,
      preferredCommunication: partner.preferredCommunication,
      profileCompleted: partner.profileCompleted,
      _creationTime: partner._creationTime,
    }));
  },
});

// List all approved partners
export const listApprovedPartners = query({
  args: {},
  handler: async (ctx) => {
    console.log("📋 Fetching approved partners...");
    
    const approvedPartners = await ctx.db
      .query("users")
      .filter((q) => 
        q.and(
          q.eq(q.field("role"), "partner"),
          q.eq(q.field("approved"), true)
        )
      )
      .collect();

    // Get assignments for each partner
    const assignments = await ctx.db.query("assignments").collect();
    const assignmentMap = assignments.reduce((acc, assignment) => {
      acc[assignment.partnerId] = assignment.salesUserId;
      return acc;
    }, {} as Record<string, string>);

    // Get sales member names
    const salesMembers = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("role"), "sales"))
      .collect();
    
    const salesMemberMap = salesMembers.reduce((acc, member) => {
      acc[member._id] = member.name || member.fullName || "Unknown";
      return acc;
    }, {} as Record<string, string>);

    console.log(`📊 Found ${approvedPartners.length} approved partners`);
    
    return approvedPartners.map(partner => ({
      _id: partner._id,
      name: partner.name,
      fullName: partner.fullName,
      email: partner.email,
      companyName: partner.companyName,
      tier: partner.tier,
      assignedSalesId: assignmentMap[partner._id],
      assignedSalesName: assignmentMap[partner._id] ? salesMemberMap[assignmentMap[partner._id]] : null,
      _creationTime: partner._creationTime,
    }));
  },
});

// Approve or reject a partner application
export const reviewPartnerApplication = mutation({
  args: {
    partnerId: v.id("users"),
    approved: v.boolean(),
    tier: v.optional(v.union(v.literal("trusted"), v.literal("elite"), v.literal("diamond"))),
    salesUserId: v.optional(v.id("users")),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, { partnerId, approved, tier, salesUserId, notes }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`🔍 Reviewing partner application: ${partnerId} - ${approved ? 'APPROVE' : 'REJECT'}`);
    
    const partner = await ctx.db.get(partnerId);
    if (!partner) {
      console.error(`❌ Partner not found: ${partnerId}`);
      throw new Error("Partner not found");
    }

    if (partner.role !== "partner") {
      console.error(`❌ User ${partnerId} is not a partner`);
      throw new Error("User is not a partner");
    }

    // Update partner approval status
    const updates: any = { approved };
    
    if (approved) {
      // Set tier if provided, otherwise default to trusted
      updates.tier = tier || "trusted";
      
      console.log(`✅ Approving partner with tier: ${updates.tier}`);
    } else {
      console.log(`❌ Rejecting partner application`);
    }

    await ctx.db.patch(partnerId, updates);

    // If approved and sales user assigned, create assignment
    if (approved && salesUserId) {
      console.log(`👥 Assigning partner to sales member: ${salesUserId}`);
      
      // Verify sales user exists and is a sales member
      const salesUser = await ctx.db.get(salesUserId);
      if (!salesUser || salesUser.role !== "sales") {
        console.error(`❌ Invalid sales user: ${salesUserId}`);
        throw new Error("Invalid sales user");
      }

      // Remove existing assignment if any
      const existingAssignment = await ctx.db
        .query("assignments")
        .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
        .first();
        
      if (existingAssignment) {
        await ctx.db.delete(existingAssignment._id);
      }

      // Create new assignment
      await ctx.db.insert("assignments", {
        partnerId,
        salesUserId,
      });

      // Update existing leads with the sales assignment
      const leads = await ctx.db
        .query("leads")
        .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
        .collect();
        
      for (const lead of leads) {
        await ctx.db.patch(lead._id, { salesUserId });
      }

      console.log(`✅ Partner assigned to sales member, updated ${leads.length} leads`);
    }

    // Log the review action
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: approved ? "PARTNER_APPROVED" : "PARTNER_REJECTED",
      entity: `users/${partnerId}`,
      meta: { 
        tier: approved ? (tier || "trusted") : undefined,
        salesUserId,
        notes,
        reviewedBy: ops.name || ops.email
      },
      at: Date.now(),
    });

    console.log(`🎉 Partner application review completed: ${partnerId}`);
    
    return { success: true };
  },
});

// Update partner tier
export const updatePartnerTier = mutation({
  args: {
    partnerId: v.id("users"),
    tier: v.union(v.literal("trusted"), v.literal("elite"), v.literal("diamond")),
  },
  handler: async (ctx, { partnerId, tier }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`🏆 Updating partner tier: ${partnerId} -> ${tier}`);
    
    const partner = await ctx.db.get(partnerId);
    if (!partner) {
      console.error(`❌ Partner not found: ${partnerId}`);
      throw new Error("Partner not found");
    }

    if (partner.role !== "partner" || !partner.approved) {
      console.error(`❌ User ${partnerId} is not an approved partner`);
      throw new Error("User is not an approved partner");
    }

    await ctx.db.patch(partnerId, { tier });

    // Log the tier update
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: "PARTNER_TIER_UPDATED",
      entity: `users/${partnerId}`,
      meta: { 
        oldTier: partner.tier,
        newTier: tier,
        updatedBy: ops.name || ops.email
      },
      at: Date.now(),
    });

    console.log(`✅ Partner tier updated successfully: ${partnerId}`);
    
    return { success: true };
  },
});

// Assign partner to sales member
export const assignPartnerToSales = mutation({
  args: {
    partnerId: v.id("users"),
    salesUserId: v.id("users"),
  },
  handler: async (ctx, { partnerId, salesUserId }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`👥 Assigning partner ${partnerId} to sales member ${salesUserId}`);
    
    // Verify partner exists and is approved
    const partner = await ctx.db.get(partnerId);
    if (!partner || partner.role !== "partner" || !partner.approved) {
      console.error(`❌ Invalid partner: ${partnerId}`);
      throw new Error("Invalid partner");
    }

    // Verify sales user exists and is a sales member
    const salesUser = await ctx.db.get(salesUserId);
    if (!salesUser || salesUser.role !== "sales") {
      console.error(`❌ Invalid sales user: ${salesUserId}`);
      throw new Error("Invalid sales user");
    }

    // Remove existing assignment if any
    const existingAssignment = await ctx.db
      .query("assignments")
      .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
      .first();
      
    if (existingAssignment) {
      await ctx.db.delete(existingAssignment._id);
    }

    // Create new assignment
    const assignmentId = await ctx.db.insert("assignments", {
      partnerId,
      salesUserId,
    });

    // Update existing leads with the sales assignment
    const leads = await ctx.db
      .query("leads")
      .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
      .collect();
      
    for (const lead of leads) {
      await ctx.db.patch(lead._id, { salesUserId });
    }

    // Log the assignment
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: "PARTNER_ASSIGNED_TO_SALES",
      entity: `assignments/${assignmentId}`,
      meta: { 
        partnerId,
        salesUserId,
        partnerName: partner.name || partner.fullName,
        salesName: salesUser.name || salesUser.fullName,
        updatedLeads: leads.length
      },
      at: Date.now(),
    });

    console.log(`✅ Partner assignment completed, updated ${leads.length} leads`);
    
    return { success: true };
  },
});

// Get partner application statistics
export const getApplicationStats = query({
  args: {},
  handler: async (ctx) => {
    console.log("📊 Calculating partner application statistics...");
    
    const allPartners = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("role"), "partner"))
      .collect();

    const pending = allPartners.filter(p => p.approved === undefined).length;
    const approved = allPartners.filter(p => p.approved === true).length;
    const rejected = allPartners.filter(p => p.approved === false).length;
    
    const tierCounts = approved > 0 ? allPartners
      .filter(p => p.approved === true)
      .reduce((acc, partner) => {
        const tier = partner.tier || "trusted";
        acc[tier] = (acc[tier] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) : {};

    console.log(`📈 Application stats: ${pending} pending, ${approved} approved, ${rejected} rejected`);
    
    return {
      total: allPartners.length,
      pending,
      approved,
      rejected,
      tierCounts,
    };
  },
});
