/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as admin from "../admin.js";
import type * as auth from "../auth.js";
import type * as authConfig from "../../docs/auth.config.ts/index.js";
import type * as deals from "../deals.js";
import type * as earnings from "../earnings.js";
import type * as http from "../http.js";
import type * as leads from "../leads.js";
import type * as lib_authz from "../lib/authz.js";
import type * as messages from "../messages.js";
import type * as migrations_addSalesTeam from "../migrations/addSalesTeam.js";
import type * as partnerApplications from "../partnerApplications.js";
import type * as referrals from "../referrals.js";
import type * as resources from "../resources.js";
import type * as router from "../router.js";
import type * as salesTeam from "../salesTeam.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  admin: typeof admin;
  auth: typeof auth;
  authConfig: typeof authConfig;
  deals: typeof deals;
  earnings: typeof earnings;
  http: typeof http;
  leads: typeof leads;
  "lib/authz": typeof lib_authz;
  messages: typeof messages;
  "migrations/addSalesTeam": typeof migrations_addSalesTeam;
  partnerApplications: typeof partnerApplications;
  referrals: typeof referrals;
  resources: typeof resources;
  router: typeof router;
  salesTeam: typeof salesTeam;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
