import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

// List all sales team members
export const listSalesMembers = query({
  args: {},
  handler: async (ctx) => {
    console.log("📋 Fetching sales team members...");
    
    const salesMembers = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("role"), "sales"))
      .collect();

    console.log(`📊 Found ${salesMembers.length} sales team members`);
    
    return salesMembers.map(member => ({
      _id: member._id,
      name: member.name,
      fullName: member.fullName,
      email: member.email,
      telegram: member.telegram,
      approved: member.approved,
      _creationTime: member._creationTime,
    }));
  },
});

// Create a new sales team member
export const createSalesMember = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    telegram: v.optional(v.string()),
  },
  handler: async (ctx, { name, email, telegram }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`🆕 Creating new sales member: ${name} (${email})`);
    
    // Check if email already exists
    const existingUser = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), email))
      .first();

    if (existingUser) {
      console.error(`❌ Email ${email} already exists`);
      throw new Error("Email already exists");
    }

    const userId = await ctx.db.insert("users", {
      name,
      fullName: name,
      email,
      telegram,
      role: "sales",
      roles: ["sales"],
      approved: true,
      profileCompleted: true,
      tier: "trusted",
    });

    // Log the creation
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: "SALES_MEMBER_CREATED",
      entity: `users/${userId}`,
      meta: { name, email, telegram },
      at: Date.now(),
    });

    console.log(`✅ Successfully created sales member: ${name}`);
    
    return { success: true, userId };
  },
});

// Update a sales team member
export const updateSalesMember = mutation({
  args: {
    userId: v.id("users"),
    name: v.optional(v.string()),
    email: v.optional(v.string()),
    telegram: v.optional(v.string()),
  },
  handler: async (ctx, { userId, name, email, telegram }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`📝 Updating sales member: ${userId}`);
    
    const user = await ctx.db.get(userId);
    if (!user) {
      console.error(`❌ Sales member not found: ${userId}`);
      throw new Error("Sales member not found");
    }

    if (user.role !== "sales") {
      console.error(`❌ User ${userId} is not a sales member`);
      throw new Error("User is not a sales member");
    }

    // Check if email already exists (if changing email)
    if (email && email !== user.email) {
      const existingUser = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("email"), email))
        .first();

      if (existingUser) {
        console.error(`❌ Email ${email} already exists`);
        throw new Error("Email already exists");
      }
    }

    const updates: any = {};
    if (name !== undefined) {
      updates.name = name;
      updates.fullName = name;
    }
    if (email !== undefined) updates.email = email;
    if (telegram !== undefined) updates.telegram = telegram;

    await ctx.db.patch(userId, updates);

    // Log the update
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: "SALES_MEMBER_UPDATED",
      entity: `users/${userId}`,
      meta: { updates },
      at: Date.now(),
    });

    console.log(`✅ Successfully updated sales member: ${userId}`);
    
    return { success: true };
  },
});

// Remove a sales team member (soft delete)
export const removeSalesMember = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, { userId }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`🗑️ Removing sales member: ${userId}`);
    
    const user = await ctx.db.get(userId);
    if (!user) {
      console.error(`❌ Sales member not found: ${userId}`);
      throw new Error("Sales member not found");
    }

    if (user.role !== "sales") {
      console.error(`❌ User ${userId} is not a sales member`);
      throw new Error("User is not a sales member");
    }

    // Soft delete by setting softDeletedAt
    await ctx.db.patch(userId, {
      softDeletedAt: Date.now(),
      approved: false,
    });

    // Remove any partner assignments
    const assignments = await ctx.db
      .query("assignments")
      .withIndex("by_sales", (q) => q.eq("salesUserId", userId))
      .collect();

    for (const assignment of assignments) {
      await ctx.db.delete(assignment._id);
    }

    // Log the removal
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: "SALES_MEMBER_REMOVED",
      entity: `users/${userId}`,
      meta: { removedAssignments: assignments.length },
      at: Date.now(),
    });

    console.log(`✅ Successfully removed sales member: ${userId}`);
    
    return { success: true };
  },
});

// Get sales member statistics
export const getSalesStats = query({
  args: {},
  handler: async (ctx) => {
    console.log("📊 Calculating sales team statistics...");
    
    const salesMembers = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("role"), "sales"))
      .collect();

    const activeSalesMembers = salesMembers.filter(member => 
      member.approved && !member.softDeletedAt
    );

    // Get assignment counts
    const assignments = await ctx.db.query("assignments").collect();
    const assignmentCounts = assignments.reduce((acc, assignment) => {
      acc[assignment.salesUserId] = (acc[assignment.salesUserId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(`📈 Sales stats: ${activeSalesMembers.length} active members, ${assignments.length} total assignments`);
    
    return {
      totalMembers: salesMembers.length,
      activeMembers: activeSalesMembers.length,
      totalAssignments: assignments.length,
      assignmentCounts,
    };
  },
});
